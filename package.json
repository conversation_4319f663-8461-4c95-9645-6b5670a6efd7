{"name": "dither-component", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@react-three/fiber": "^9.2.0", "@react-three/postprocessing": "^3.0.4", "gsap": "^3.13.0", "postprocessing": "^6.34.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.6.3", "three": "^0.160.0"}, "devDependencies": {"@types/node": "^24.0.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/three": "^0.160.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^7.0.4"}}